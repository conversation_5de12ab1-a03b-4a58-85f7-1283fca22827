"use client";

import React, { useState, useEffect, Suspense } from 'react'; // Import Suspense
import { useRouter, useSearchParams } from 'next/navigation';
import Container from '@/components/Container';
import Section from '@/components/Section';

const questions = [
    {
        text: "Are you a man or a woman?",
        type: "gender",
        options: ["Man", "Woman"]
    },
    {
        text: "Are you getting this app for yourself?",
        type: "boolean"
    },
    {
        text: "Which age group do you belong to?",
        type: "age_group",
        options: ["Under 18", "18-24", "25-34", "35-44", "45-54", "55-64", "65+"]
    },
    {
        text: "Do you want to easily understand the sugar in your food?",
        type: "boolean"
    },
    {
        text: "What is the most important feature you would like to see in the app? (Select all that apply)",
        type: "multiselect",
        options: [
            "Food photo analysis",
            "Detailed sugar tracking",
            "Personalized dietary advice",
            "Community support"
        ]
    }
];

const SurveyPageContent: React.FC = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [email, setEmail] = useState('');
    const [currentStep, setCurrentStep] = useState(0);
    const [answers, setAnswers] = useState<(boolean | string | string[])[]>(Array(questions.length).fill('')); // Initialize with empty string for single-select, empty array for multiselect
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        const userEmail = searchParams.get('email');
        if (userEmail) {
            setEmail(userEmail);
            // Initialize answers for specific types
            setAnswers(prevAnswers => {
                const newAnswers = [...prevAnswers];
                if (questions[0].type === "gender") newAnswers[0] = ''; // Ensure gender is initialized as empty string
                if (questions[2].type === "age_group") newAnswers[2] = ''; // Ensure age_group is initialized as empty string
                if (questions[questions.length - 1].type === "multiselect") newAnswers[questions.length - 1] = []; // Ensure multiselect is initialized as empty array
                return newAnswers;
            });
        } else {
            router.push('/');
        }
    }, [searchParams, router]);

    const handleAnswer = async (answer: boolean | string) => {
        const newAnswers = [...answers];
        const currentQuestionType = questions[currentStep].type;

        if (currentQuestionType === "boolean") {
            newAnswers[currentStep] = answer as boolean;
            setAnswers(newAnswers);
            setCurrentStep(currentStep + 1);
        } else if (currentQuestionType === "gender" || currentQuestionType === "age_group") {
            newAnswers[currentStep] = answer as string;
            setAnswers(newAnswers);
            setCurrentStep(currentStep + 1);
        } else if (currentQuestionType === "multiselect") {
            const currentSelections = (newAnswers[currentStep] || []) as string[];
            if (currentSelections.includes(answer as string)) {
                newAnswers[currentStep] = currentSelections.filter(item => item !== answer);
            } else {
                newAnswers[currentStep] = [...currentSelections, answer as string];
            }
            setAnswers(newAnswers);
            // Do not advance step automatically for multiselect
        }
    };

    const handleSubmitSurvey = async (finalAnswers: (boolean | string | string[])[]) => {
        setLoading(true);
        try {
            const response = await fetch('/api/survey', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email, answers: finalAnswers }),
            });

            if (response.ok) {
                router.push('/thank-you');
            } else {
                const errorData = await response.json();
                alert(`Failed to submit survey: ${errorData.message || 'An unknown error occurred.'}`);
            }
        } catch (error) {
            console.error('Error submitting survey:', error);
            alert('An unexpected error occurred. Please check your internet connection and try again.');
        } finally {
            setLoading(false);
        }
    };

    if (!email) {
        return (
            <Section id="loading-survey" title="Loading Survey..." description="Please ensure you arrived here from the newsletter signup.">
                <Container>
                    <p>If you are not redirected, please return to the homepage.</p>
                </Container>
            </Section>
        );
    }

    const currentQuestion = questions[currentStep];

    return (
        <Section id="priority-queue-survey" title="Get Priority Access!" description={`Fill out this quick survey to secure your priority spot on our waiting list, ${email}.`} className="mt-20">
            <Container>

                <div className="bg-white p-8 rounded-lg shadow-lg max-w-md mx-auto">
                    <div className="text-center text-xl font-semibold mb-6">
                        Question {currentStep + 1} of {questions.length}
                    </div>
                    <p className="text-center text-gray-800 text-lg mb-8">
                        {currentQuestion.text}
                    </p>
                    {currentQuestion.type === "boolean" ? (
                        <div className="flex justify-center gap-4">
                            <button
                                onClick={() => handleAnswer(true)}
                                className={`px-10 py-4 rounded-md text-xl font-semibold transition-colors duration-200 ${
                                    answers[currentStep] === true
                                        ? 'bg-green-500 text-white'
                                        : 'bg-gray-200 text-gray-800 hover:bg-green-100'
                                }`}
                                disabled={loading}
                            >
                                Yes
                            </button>
                            <button
                                onClick={() => handleAnswer(false)}
                                className={`px-10 py-4 rounded-md text-xl font-semibold transition-colors duration-200 ${
                                    answers[currentStep] === false
                                        ? 'bg-red-500 text-white'
                                        : 'bg-gray-200 text-gray-800 hover:bg-red-100'
                                }`}
                                disabled={loading}
                            >
                                No
                            </button>
                        </div>
                    ) : currentQuestion.type === "gender" || currentQuestion.type === "age_group" ? (
                        <div className="flex flex-col gap-4">
                            {currentQuestion.options?.map((option, index) => (
                                <button
                                    key={index}
                                    onClick={() => handleAnswer(option)}
                                    className={`px-6 py-3 rounded-md text-lg font-semibold transition-colors duration-200 ${
                                        answers[currentStep] === option
                                            ? 'bg-blue-500 text-white'
                                            : 'bg-gray-200 text-gray-800 hover:bg-blue-100'
                                    }`}
                                    disabled={loading}
                                >
                                    {option}
                                </button>
                            ))}
                        </div>
                    ) : ( // multiselect
                        <div className="flex flex-col gap-4">
                            {currentQuestion.options?.map((option, index) => {
                                const isSelected = (answers[currentStep] as string[]).includes(option);
                                return (
                                    <button
                                        key={index}
                                        onClick={() => handleAnswer(option)}
                                        className={`px-6 py-3 rounded-md text-lg font-semibold transition-colors duration-200 ${
                                            isSelected
                                                ? 'bg-blue-500 text-white'
                                                : 'bg-gray-200 text-gray-800 hover:bg-blue-100'
                                        }`}
                                        disabled={loading}
                                    >
                                        {option}
                                    </button>
                                );
                            })}
                            <button
                                onClick={() => handleSubmitSurvey(answers)}
                                className="mt-6 px-8 py-3 rounded-md bg-orange-500 text-white font-semibold hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                                disabled={loading}
                            >
                                Submit Survey
                            </button>
                        </div>
                    )}
                </div>
            </Container>
        </Section>
    );
};

export default function SurveyPage() {
    return (
        <Suspense fallback={<Section id="loading-survey" title="Loading Survey..." description="Please wait while the survey loads."><Container><p>Loading...</p></Container></Section>}>
            <SurveyPageContent />
        </Suspense>
    );
}
