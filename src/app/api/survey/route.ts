import { NextResponse } from 'next/server';

export async function POST(request: Request) {
    try {
        const { email, answers } = await request.json();

        if (!email || !answers || !Array.isArray(answers)) {
            return NextResponse.json({ message: 'Invalid request body.' }, { status: 400 });
        }

        // Define the Make.com webhook URL
        const MAKE_WEBHOOK_URL = process.env.MAKE_WEBHOOK_URL || "https://hook.us1.make.com/q574pvggwbhh5x641bbfsx8mlwh84ivq";

        if (!MAKE_WEBHOOK_URL) {
            return NextResponse.json({ message: 'Make.com Webhook URL is not configured.' }, { status: 500 });
        }

        // Prepare data for Make.com
        const payload = {
            email,
            answers,
            timestamp: new Date().toISOString(),
        };

        console.log('Sending payload to Make.com:', payload); // Log the payload

        const makeResponse = await fetch(MAKE_WEBHOOK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        });

        console.log('Make.com response status:', makeResponse.status); // Log the response status

        if (makeResponse.ok) {
            return NextResponse.json({ message: 'Survey data sent to Make.com successfully!' }, { status: 200 });
        } else {
            let errorData;
            const contentType = makeResponse.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                errorData = await makeResponse.json();
            } else {
                errorData = await makeResponse.text();
            }
            console.error('Make.com webhook error:', errorData);
            return NextResponse.json({ message: `Failed to send data to Make.com webhook: ${typeof errorData === 'string' ? errorData : errorData.message || 'Unknown error'}` }, { status: makeResponse.status });
        }

    } catch (error) {
        console.error('Error processing survey submission:', error);
        return NextResponse.json({ message: 'Internal server error.' }, { status: 500 });
    }
}
