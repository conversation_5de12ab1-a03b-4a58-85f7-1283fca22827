import { NextResponse } from 'next/server';

export async function POST(request: Request) {
    const { email } = await request.json();

    if (!email) {
        return NextResponse.json({ message: 'Email is required.' }, { status: 400 });
    }

    // Server-side email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        return NextResponse.json({ message: 'Invalid email address format.' }, { status: 400 });
    }

    const RESEND_API_KEY = process.env.RESEND_API_KEY;
    const RESEND_AUDIENCE_ID = process.env.RESEND_AUDIENCE_ID;

    if (!RESEND_API_KEY || !RESEND_AUDIENCE_ID) {
        return NextResponse.json({ message: 'Server configuration error: Resend API key or Audience ID missing.' }, { status: 500 });
    }

    try {
        const response = await fetch(`https://api.resend.com/audiences/${RESEND_AUDIENCE_ID}/contacts`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${RESEND_API_KEY}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, unsubscribed: false }),
        });

        if (response.ok) {
            return NextResponse.json({ message: 'Successfully subscribed!' }, { status: 200 });
        } else {
            const errorData = await response.json();
            console.error('Resend API error:', errorData);
            return NextResponse.json({ message: errorData.message || 'Failed to subscribe via Resend API' }, { status: response.status });
        }
    } catch (error) {
        console.error('Error calling Resend API:', error);
        return NextResponse.json({ message: 'Internal server error' }, { status: 500 });
    }
}
