"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import Container from '@/components/Container';
import Section from '@/components/Section';

const ThankYouPage: React.FC = () => {
    const router = useRouter();

    return (
        <Section id="thank-you" title="Congrats!" description="You've been successfully added to our waiting list.">
            <Container>
                <button
                    onClick={() => router.push('/')}
                    className="mt-4 px-8 py-3 rounded-md bg-orange-500 text-white font-semibold hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                >
                    Return to Home
                </button>
            </Container>
        </Section>
    );
};

export default ThankYouPage;
