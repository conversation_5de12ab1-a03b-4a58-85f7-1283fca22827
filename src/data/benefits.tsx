import { FiBarChart2, FiTarget, FiTrendingUp, FiMaximize, FiActivity } from "react-icons/fi";

import { IBenefit } from "@/types"

export const benefits: IBenefit[] = [
    {
        title: "Smart Food Analysis",
        description: "Take the guesswork out of managing your diet. Our AI-powered analysis identifies food components and flags those unsuitable for diabetics.",
        bullets: [
            {
                title: "Instant Component Breakdown",
                description: "Quickly see a detailed breakdown of your meal's nutritional components.",
                icon: <FiBarChart2 size={26} />
            },
            {
                title: "Diabetic-Friendly Flags",
                description: "Get immediate alerts for ingredients that may impact blood sugar levels.",
                icon: <FiTarget size={26} />
            },
            {
                title: "Personalized Dietary Insights",
                description: "Receive tailored recommendations based on your specific diabetic needs.",
                icon: <FiTrendingUp size={26} />
            }
        ],
        imageSrc: "/images/mockup-1.webp"
    },
    {
        title: "Seamless Meal Planning",
        description: "Start building healthier habits today. Platepal makes meal planning accessible and straightforward for diabetics.",
        bullets: [
            {
                title: "Nutrient Tracking",
                description: "Keep a detailed log of your daily nutrient intake to better manage your diet.",
                icon: <FiActivity size={26} />
            },
            {
                title: "Portion Control Guidance",
                description: "Understand ideal portion sizes for balanced meals.",
                icon: <FiMaximize size={26} />
            },
            {
                title: "Dietary Tracking",
                description: "Monitor your daily intake and progress with easy-to-understand metrics.",
                icon: <FiActivity size={26} />
            }
        ],
        imageSrc: "/images/mockup-2.webp"
    },
]
