import { IFAQ } from "@/types";
import { siteDetails } from "./siteDetails";

export const faqs: IFAQ[] = [
    {
        question: `Is ${siteDetails.siteName} secure?`,
        answer: 'Absolutely. We use industry-standard encryption to protect your dietary data and ensure your privacy. Your health information is always safe with us.',
    },
    {
        question: `Can I use ${siteDetails.siteName} on multiple devices?`,
        answer: 'Yes! Your Platepal account syncs seamlessly across all your devices - smartphone, tablet, and computer, so you can track your meals anywhere.',
    },
    {
        question: 'How does Platepal identify unsuitable food components?',
        answer: '<PERSON>pal uses advanced AI and image recognition technology to analyze photos of your meals. It identifies individual food components and cross-references them with a comprehensive database to flag ingredients that may be unsuitable for diabetics.',
    },
    {
        question: 'Do I need to be a nutritionist to use Platepal?',
        answer: 'Not at all! Platepal is designed for everyone. Our intuitive interface and clear insights make it easy to understand your food choices, regardless of your dietary knowledge.',
    },
    {
        question: 'What if I have specific dietary restrictions beyond diabetes?',
        answer: 'Platepal is continuously evolving to support a wider range of dietary needs. While our primary focus is on diabetes, we aim to provide customizable insights for various restrictions in future updates. Please contact support for more information.'
    }
];
