import { IMenuItem, ISocials } from "@/types";

export const footerDetails: {
    subheading: string;
    quickLinks: IMenuItem[];
    email: string;
    socials: ISocials;
} = {
    subheading: "Your AI-powered companion for confident eating.",
    quickLinks: [
        {
            text: "Home",
            url: "/"
        },
        {
            text: "Features",
            url: "#features"
        },
        {
            text: "Join List",
            url: "#cta"
        }
    ],
    email: '<EMAIL>',
    socials: {
        // github: 'https://github.com',
        // x: 'https://twitter.com/x',
        twitter: 'https://twitter.com/Twitter',
        facebook: 'https://facebook.com',
        // youtube: 'https://youtube.com',
        linkedin: 'https://www.linkedin.com',
        // threads: 'https://www.threads.net',
        instagram: 'https://www.instagram.com',
    }
}
