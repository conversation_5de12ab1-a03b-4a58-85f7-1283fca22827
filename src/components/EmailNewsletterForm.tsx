"use client";

import React, { useState } from 'react';

interface EmailNewsletterFormProps {
    buttonText?: string;
    inputPlaceholder?: string;
    className?: string;
}

const EmailNewsletterForm: React.FC<EmailNewsletterFormProps> = ({
    buttonText = 'Join Waiting List',
    inputPlaceholder = 'Enter your email',
    className = ''
}) => {
    const [email, setEmail] = useState('');
    const [loading, setLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Basic client-side email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            alert('Please enter a valid email address (e.g., <EMAIL>).');
            return;
        }

        setLoading(true);

        try {
            const response = await fetch('/api/resend', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email }),
            });

            if (response.ok) {
                // Redirect to the survey page, passing the email as a query parameter
                window.location.href = `/survey?email=${encodeURIComponent(email)}`;
            } else {
                const errorData = await response.json();
                alert(`Failed to join waiting list: ${errorData.message || 'An unknown error occurred.'}`);
            }
        } catch (error) {
            console.error('Error submitting email:', error);
            alert('An unexpected error occurred. Please check your internet connection and try again.');
        } finally {
            setLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit} className={`flex flex-col items-center gap-4 w-full ${className}`}>
            <input
                type="email"
                placeholder={inputPlaceholder}
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full px-4 py-2 rounded-md text-gray-900 bg-white border border-gray-300 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
            <button
                type="submit"
                className="w-full px-8 py-3 rounded-md bg-orange-500 text-white font-semibold hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={loading}
            >
                {loading ? 'Joining...' : buttonText}
            </button>
        </form>
    );
};

export default EmailNewsletterForm;
