"use client";

import React from 'react';
import { usePathname } from 'next/navigation';
import Header from "@/components/Header";
import Footer from "@/components/Footer";

interface LayoutWrapperProps {
  children: React.ReactNode;
}

const LayoutWrapper: React.FC<LayoutWrapperProps> = ({ children }) => {
  const pathname = usePathname();
  const hideFooter = pathname === '/survey';

  return (
    <>
      <Header />
      <main>
        {children}
      </main>
      {!hideFooter && <Footer />}
    </>
  );
};

export default LayoutWrapper;
